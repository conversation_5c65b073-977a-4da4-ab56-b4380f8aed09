import '../styles/design.css'
import { useNavigate } from 'react-router-dom';

function Design1() {
    const navigate = useNavigate();

    return (
        <>
            
            <div className='design-1' id='s'>s</div>
            <div className='design2'></div>
            <div className='design3' onClick={() => navigate('/car')}>Diesel</div>
            <div className='design4' onClick={() => navigate('/Shop')}>Shop</div>
            <div className='design-words'>
                <div className='design5'>Diesel<hr id='hr-1' /></div>
                <div className='design6'>Watches<hr id='hr-2' /></div>
                <div className='design7'>Tough<hr id='hr-3' /></div>
                <div className='design8'>Modern<hr id='hr-4' /></div>
            </div>
            <div className='center-design'>
                <div className='design9'>P</div>
                <div className='design10'>O</div>
                <div className='design11'>R</div>
                <div className='design12'>C</div>
                <div className='design13'>H</div>
                <div className='design14'>E</div>
            </div>
            <div className='design15' onClick={() => navigate('/Cart')}>Cart</div>
            <div className='design16' onClick={() => navigate('/login/register')}>Login/Register</div>
            <div className='design17' onClick={() => navigate('/Profile')}>Profile</div>
            <svg className="blinking-down-arrow" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="6 9 12 15 18 9" />
            </svg>

        </>
    );
}

export default Design1;
