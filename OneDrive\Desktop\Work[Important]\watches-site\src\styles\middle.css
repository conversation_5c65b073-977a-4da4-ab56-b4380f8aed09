@font-face {
  font-family: 'Boldivia';
  src: url('../src/assets/fonts/boldovia.regular.ttf') format('truetype');
  font-style: normal;
  font-weight: normal;
}

.middle-background {
  position: relative;
  width: 100%;
  height: 100%;
  left: 0;
  z-index: 0;
  background: rgb(73, 73, 73);
  min-height: 100vh;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
}


.parent {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-auto-rows: minmax(200px, auto);
  gap: 20px;
  padding: 20px;
  background: linear-gradient(to right, #c2c2c3 50%, #232738 50%);
  max-height: 500px;
}

/* Grid Item Sizes to Match Screenshot */
.div1 {
  grid-row: span 2;
  cursor: pointer;
}

.div2 {
  grid-column: 2;
  grid-row: 1;
  cursor: pointer;
}

.div3 {
  grid-column: 2;
  grid-row: 2;
  cursor: pointer;
}

.div4 {
  grid-column: 3;
  grid-row: 1;
  cursor: pointer;
}

.div5 {
  grid-column: 3;
  grid-row: 2;
  cursor: pointer;
}

.div1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.div5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

/* Optional: Media Styling */
img,
video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/*===========================Marquee start=================================================================*/
.marquee-tilted-wrapper {
  overflow: hidden;
  background: #ffffff;
}

.marquee-wrapper {
  overflow: hidden;
  width: 100%;
}

.marquee-content {
  display: flex;
  gap: 4rem;
  min-width: 100%;
  white-space: nowrap;
  animation: scrollMarquee 15s linear infinite;
}

.marquee-content span {
  font-size: 2rem;
  font-weight: bold;
  color: #d35400;
}

/* Keyframes for infinite scroll */
@keyframes scrollMarquee {
  0% {
    transform: translateX(100%);
  }

  100% {
    transform: translateX(-150%);
  }
}

/*===========================Marquee end=================================================================*/

.banner {
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  border-radius: 0;
  width: 100%;
  object-fit: fill;
  object-position: center;
}

#Banner1 {
  width: 100%;
  height: 100%;
  border-radius: 0%;
  object-fit: fill;
  object-position: center;
}


#Banner1 {
  width: 100%;
  height: 600px;
  display: block;
}

#banner-heading {
  position: absolute;
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  top: 15%;
  left: 30%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(222, 222, 222);
  font-size: 5rem;
  font-weight: bold;

  pointer-events: none;
}

#banner-subtittle {
  position: absolute;
  top: 32%;
  left: 28%;
  transform: translate(-50%, -50%);
  font-family: 'boldivia';
  color: rgb(216, 216, 216);
  font-size: 2.5rem;
  font-weight: bold;

  pointer-events: none;
}

#banner-button {
  position: absolute;
  top: 50%;
  left: 12%;
  color: rgb(0, 0, 0);
  font-size: 1.25rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.855);
  cursor: pointer;
}


/* From Uiverse.io by marcelodolza */
.button {
  --white: #ffe7ff;
  --bg: #080808;
  --radius: 100px;
  outline: none;

  cursor: pointer;
  border: 0;
  position: relative;
  border-radius: var(--radius);
  background-color: var(--bg);
  transition: all 0.2s ease;
  box-shadow:
    inset 0 0.3rem 0.9rem rgba(255, 255, 255, 0.3),
    inset 0 -0.1rem 0.3rem rgba(0, 0, 0, 0.7),
    inset 0 -0.4rem 0.9rem rgba(255, 255, 255, 0.5),
    0 3rem 3rem rgba(0, 0, 0, 0.3),
    0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
}

.button .wrap {
  font-size: 25px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  padding: 32px 45px;
  border-radius: inherit;
  position: relative;
  overflow: hidden;
}

.button .wrap p span:nth-child(2) {
  display: none;
}

.button:hover .wrap p span:nth-child(1) {
  display: none;
}

.button:hover .wrap p span:nth-child(2) {
  display: inline-block;
}

.button .wrap p {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  transition: all 0.2s ease;
  transform: translateY(2%);
  mask-image: linear-gradient(to bottom, white 40%, transparent);
}

.button .wrap::before,
.button .wrap::after {
  content: "";
  position: absolute;
  transition: all 0.3s ease;
}

.button .wrap::before {
  left: -15%;
  right: -15%;
  bottom: 25%;
  top: -100%;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.12);
}

.button .wrap::after {
  left: 6%;
  right: 6%;
  top: 12%;
  bottom: 40%;
  border-radius: 22px 22px 0 0;
  box-shadow: inset 0 10px 8px -10px rgba(255, 255, 255, 0.8);
  background: linear-gradient(180deg,
      rgba(255, 255, 255, 0.3) 0%,
      rgba(0, 0, 0, 0) 50%,
      rgba(0, 0, 0, 0) 100%);
}

.button:hover {
  box-shadow:
    inset 0 0.3rem 0.5rem rgba(255, 255, 255, 0.4),
    inset 0 -0.1rem 0.3rem rgba(0, 0, 0, 0.7),
    inset 0 -0.4rem 0.9rem rgba(255, 255, 255, 0.7),
    0 3rem 3rem rgba(0, 0, 0, 0.3),
    0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
}

.button:hover .wrap::before {
  transform: translateY(-5%);
}

.button:hover .wrap::after {
  opacity: 0.4;
  transform: translateY(5%);
}

.button:hover .wrap p {
  transform: translateY(-4%);
}

.button:active {
  transform: translateY(4px);
  box-shadow:
    inset 0 0.3rem 0.5rem rgba(255, 255, 255, 0.5),
    inset 0 -0.1rem 0.3rem rgba(0, 0, 0, 0.8),
    inset 0 -0.4rem 0.9rem rgba(255, 255, 255, 0.4),
    0 3rem 3rem rgba(0, 0, 0, 0.3),
    0 1rem 1rem -0.6rem rgba(0, 0, 0, 0.8);
}




/*====================Trending Now start=====================================================*/

.headings {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-family: "boldivia";
  background: rgb(222, 222, 222);
}

#main-heading {
  color: #000000;
  font-size: 5rem;
  margin-top: 0rem;
}

#subtittle {
  color: rgb(0, 0, 0);
  font-size: 1rem;
  margin-top: -4rem;
}


.trending-images {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
  background: rgb(222, 222, 222);
  max-height: 380px;
}

.trend1 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend2 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend3 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend4 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend5 {
  grid-row: span 4 / span 4;
  height: 350px;
  width: 280px;
  cursor: pointer;
}

.trend1:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend2:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend3:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend4:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}

.trend5:hover {
  transform: scale(1.02);
  transition: transform 0.2s;
}



/*====================Trending Now end=====================================================*/